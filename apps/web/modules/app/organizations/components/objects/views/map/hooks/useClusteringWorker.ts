import { useCallback, useEffect, useRef, useState } from 'react';
import type { 
	ClusteringMessage, 
	ClusteringResponse, 
	PropertyData, 
	ClusterResult, 
	ViewportBounds 
} from '../workers/clustering.worker';

interface UseClusteringWorkerOptions {
	enabled?: boolean;
	maxConcurrentTasks?: number;
}

interface ClusteringTask {
	id: string;
	type: ClusteringMessage['type'];
	promise: Promise<any>;
	resolve: (value: any) => void;
	reject: (error: Error) => void;
	startTime: number;
}

export function useClusteringWorker(options: UseClusteringWorkerOptions = {}) {
	const { enabled = true, maxConcurrentTasks = 3 } = options;
	
	const workerRef = useRef<Worker | null>(null);
	const tasksRef = useRef<Map<string, ClusteringTask>>(new Map());
	const [isWorkerReady, setIsWorkerReady] = useState(false);
	const [performance, setPerformance] = useState({
		totalTasks: 0,
		completedTasks: 0,
		averageProcessingTime: 0,
		errorCount: 0,
	});

	// Initialize Web Worker
	useEffect(() => {
		if (!enabled || typeof window === 'undefined') return;

		try {
			// Create the worker
			const worker = new Worker(
				new URL('../workers/clustering.worker.ts', import.meta.url),
				{ type: 'module' }
			);

			worker.onmessage = (e: MessageEvent<ClusteringResponse>) => {
				const { requestId, type, data, processingTime } = e.data;
				const task = tasksRef.current.get(requestId);

				if (!task) {
					console.warn(`Received response for unknown task: ${requestId}`);
					return;
				}

				// Update performance metrics
				setPerformance(prev => ({
					totalTasks: prev.totalTasks,
					completedTasks: prev.completedTasks + 1,
					averageProcessingTime: ((prev.averageProcessingTime * prev.completedTasks) + processingTime) / (prev.completedTasks + 1),
					errorCount: type === 'ERROR' ? prev.errorCount + 1 : prev.errorCount,
				}));

				// Handle the response
				if (type === 'ERROR') {
					task.reject(new Error(data.error || 'Unknown worker error'));
				} else {
					task.resolve(data);
				}

				// Clean up the task
				tasksRef.current.delete(requestId);
			};

			worker.onerror = (error) => {
				console.error('Clustering worker error:', error);
				// Reject all pending tasks
				for (const [taskId, task] of Array.from(tasksRef.current.entries())) {
					task.reject(new Error('Worker crashed'));
					tasksRef.current.delete(taskId);
				}
				setIsWorkerReady(false);
			};

			workerRef.current = worker;
			setIsWorkerReady(true);

			return () => {
				worker.terminate();
				workerRef.current = null;
				setIsWorkerReady(false);
				tasksRef.current.clear();
			};
		} catch (error) {
			console.error('Failed to initialize clustering worker:', error);
			setIsWorkerReady(false);
		}
	}, [enabled]);

	// Generic task execution function
	const executeTask = useCallback(<T>(
		type: ClusteringMessage['type'],
		data: any
	): Promise<T> => {
		return new Promise((resolve, reject) => {
			if (!workerRef.current || !isWorkerReady) {
				reject(new Error('Worker not available'));
				return;
			}

			// Check if we have too many concurrent tasks
			if (tasksRef.current.size >= maxConcurrentTasks) {
				reject(new Error('Too many concurrent tasks'));
				return;
			}

			const requestId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
			
			const task: ClusteringTask = {
				id: requestId,
				type,
				promise: Promise.resolve(), // Will be overwritten
				resolve,
				reject,
				startTime: Date.now(),
			};

			tasksRef.current.set(requestId, task);

			// Update performance metrics
			setPerformance(prev => ({
				...prev,
				totalTasks: prev.totalTasks + 1,
			}));

			// Send message to worker
			const message: ClusteringMessage = {
				type,
				data,
				requestId,
			};

			workerRef.current.postMessage(message);
		});
	}, [isWorkerReady, maxConcurrentTasks]);

	// Cluster properties
	const clusterProperties = useCallback((
		properties: PropertyData[],
		bounds: ViewportBounds,
		zoom: number,
		options?: {
			maxClusters?: number;
			minClusterSize?: number;
			gridSizeMultiplier?: number;
		}
	): Promise<ClusterResult[]> => {
		return executeTask('CLUSTER_PROPERTIES', {
			properties,
			bounds,
			zoom,
			options,
		});
	}, [executeTask]);

	// Calculate optimal bounds
	const calculateOptimalBounds = useCallback((
		properties: PropertyData[]
	): Promise<ViewportBounds | null> => {
		return executeTask('CALCULATE_BOUNDS', { properties });
	}, [executeTask]);

	// Optimize viewport strategy
	const optimizeViewport = useCallback((
		properties: PropertyData[],
		bounds: ViewportBounds,
		zoom: number
	): Promise<{
		strategy: 'cluster' | 'individual' | 'mixed';
		reasoning: string;
		recommendations: string[];
	}> => {
		return executeTask('OPTIMIZE_VIEWPORT', {
			properties,
			bounds,
			zoom,
		});
	}, [executeTask]);

	// Cancel all pending tasks
	const cancelAllTasks = useCallback(() => {
		for (const [taskId, task] of Array.from(tasksRef.current.entries())) {
			task.reject(new Error('Task cancelled'));
			tasksRef.current.delete(taskId);
		}
	}, []);

	// Get current worker status
	const getWorkerStatus = useCallback(() => {
		return {
			isReady: isWorkerReady,
			pendingTasks: tasksRef.current.size,
			performance,
			canAcceptTasks: isWorkerReady && tasksRef.current.size < maxConcurrentTasks,
		};
	}, [isWorkerReady, maxConcurrentTasks, performance]);

	return {
		// Main clustering functions
		clusterProperties,
		calculateOptimalBounds,
		optimizeViewport,
		
		// Worker management
		isWorkerReady,
		cancelAllTasks,
		getWorkerStatus,
		
		// Performance metrics
		performance,
		
		// Status
		pendingTasks: tasksRef.current.size,
		canAcceptTasks: isWorkerReady && tasksRef.current.size < maxConcurrentTasks,
	};
} 
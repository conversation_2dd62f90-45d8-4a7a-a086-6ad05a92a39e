// Web Worker for client-side property clustering
// This handles heavy computational tasks without blocking the main UI thread

export interface ClusteringMessage {
	type: 'CLUSTER_PROPERTIES' | 'CALCULATE_BOUNDS' | 'OPTIMIZE_VIEWPORT';
	data: any;
	requestId: string;
}

export interface ClusteringResponse {
	type: 'CLUSTERING_COMPLETE' | 'BOUNDS_CALCULATED' | 'VIEWPORT_OPTIMIZED' | 'ERROR';
	data: any;
	requestId: string;
	processingTime: number;
}

export interface PropertyData {
	id: string;
	name: string;
	position: [number, number]; // [lng, lat]
	propertyType: string;
	price?: number;
	metadata?: Record<string, any>;
}

export interface ClusterResult {
	id: string;
	position: [number, number];
	count: number;
	bounds: {
		north: number;
		south: number;
		east: number;
		west: number;
	};
	properties: PropertyData[];
	averagePrice?: number;
	propertyTypes: string[];
	isCluster: true;
}

export interface ViewportBounds {
	north: number;
	south: number;
	east: number;
	west: number;
}

// Grid-based clustering algorithm optimized for performance
function clusterProperties(
	properties: PropertyData[],
	bounds: ViewportBounds,
	zoom: number,
	options: {
		maxClusters?: number;
		minClusterSize?: number;
		gridSizeMultiplier?: number;
	} = {}
): ClusterResult[] {
	const {
		maxClusters = 50,
		minClusterSize = zoom < 8 ? 10 : zoom < 12 ? 5 : 3,
		gridSizeMultiplier = 1.0,
	} = options;

	if (properties.length === 0) return [];

	// Calculate adaptive grid size based on zoom and viewport
	const latDiff = bounds.north - bounds.south;
	const lngDiff = bounds.east - bounds.west;
	const baseGridSize = Math.max(latDiff, lngDiff) / Math.sqrt(maxClusters);
	const gridSize = baseGridSize * gridSizeMultiplier * (20 - zoom) / 10;

	// Create grid cells
	const gridCells = new Map<string, PropertyData[]>();

	// Group properties into grid cells
	for (const property of properties) {
		const [lng, lat] = property.position;
		
		// Check if property is within bounds
		if (lng < bounds.west || lng > bounds.east || lat < bounds.south || lat > bounds.north) {
			continue;
		}

		const cellX = Math.floor(lng / gridSize);
		const cellY = Math.floor(lat / gridSize);
		const cellKey = `${cellX},${cellY}`;

		if (!gridCells.has(cellKey)) {
			gridCells.set(cellKey, []);
		}
		gridCells.get(cellKey)!.push(property);
	}

	// Convert grid cells to clusters
	const clusters: ClusterResult[] = [];
	
	for (const [cellKey, cellProperties] of Array.from(gridCells.entries())) {
		if (cellProperties.length < minClusterSize) continue;

		// Calculate cluster center and bounds
		const lngs = cellProperties.map((p: PropertyData) => p.position[0]);
		const lats = cellProperties.map((p: PropertyData) => p.position[1]);
		
		const centerLng = lngs.reduce((sum: number, lng: number) => sum + lng, 0) / lngs.length;
		const centerLat = lats.reduce((sum: number, lat: number) => sum + lat, 0) / lats.length;

		const clusterBounds = {
			west: Math.min(...lngs),
			east: Math.max(...lngs),
			south: Math.min(...lats),
			north: Math.max(...lats),
		};

		// Calculate aggregate data
		const prices = cellProperties
			.map((p: PropertyData) => p.price)
			.filter((price): price is number => typeof price === 'number' && price > 0);
		
		const averagePrice = prices.length > 0 
			? prices.reduce((sum: number, price: number) => sum + price, 0) / prices.length 
			: undefined;

		const propertyTypes = Array.from(new Set(cellProperties.map((p: PropertyData) => p.propertyType).filter(Boolean)));

		clusters.push({
			id: `cluster-${cellKey}`,
			position: [centerLng, centerLat],
			count: cellProperties.length,
			bounds: clusterBounds,
			properties: cellProperties,
			averagePrice,
			propertyTypes,
			isCluster: true,
		});
	}

	// Sort clusters by property count (largest first) and limit results
	return clusters
		.sort((a, b) => b.count - a.count)
		.slice(0, maxClusters);
}

// Calculate optimal viewport bounds for a set of properties
function calculateOptimalBounds(properties: PropertyData[]): ViewportBounds | null {
	if (properties.length === 0) return null;

	const lngs = properties.map(p => p.position[0]);
	const lats = properties.map(p => p.position[1]);

	const bounds = {
		west: Math.min(...lngs),
		east: Math.max(...lngs),
		south: Math.min(...lats),
		north: Math.max(...lats),
	};

	// Add padding (10% of the range)
	const lngPadding = (bounds.east - bounds.west) * 0.1;
	const latPadding = (bounds.north - bounds.south) * 0.1;

	return {
		west: bounds.west - lngPadding,
		east: bounds.east + lngPadding,
		south: bounds.south - latPadding,
		north: bounds.north + latPadding,
	};
}

// Viewport optimization - determine best strategy for current view
function optimizeViewportStrategy(
	properties: PropertyData[],
	bounds: ViewportBounds,
	zoom: number
): {
	strategy: 'cluster' | 'individual' | 'mixed';
	reasoning: string;
	recommendations: string[];
} {
	const propertiesInView = properties.filter(p => {
		const [lng, lat] = p.position;
		return lng >= bounds.west && lng <= bounds.east && lat >= bounds.south && lat <= bounds.north;
	});

	const density = propertiesInView.length / ((bounds.east - bounds.west) * (bounds.north - bounds.south));
	const recommendations: string[] = [];

	let strategy: 'cluster' | 'individual' | 'mixed' = 'individual';
	let reasoning = '';

	if (zoom < 10) {
		strategy = 'cluster';
		reasoning = 'Low zoom level - clustering recommended for performance';
		recommendations.push('Use large clusters to reduce visual complexity');
	} else if (zoom < 14) {
		if (propertiesInView.length > 1000) {
			strategy = 'cluster';
			reasoning = 'High property density - clustering needed for performance';
			recommendations.push('Consider smaller cluster sizes at this zoom level');
		} else {
			strategy = 'mixed';
			reasoning = 'Medium zoom - mix of clusters and individual properties';
			recommendations.push('Show individual properties in sparse areas');
		}
	} else {
		if (propertiesInView.length > 500) {
			strategy = 'mixed';
			reasoning = 'High zoom with many properties - selective clustering';
			recommendations.push('Cluster only dense areas');
		} else {
			strategy = 'individual';
			reasoning = 'High zoom with manageable property count';
			recommendations.push('Show all individual properties for detail');
		}
	}

	// Performance recommendations
	if (propertiesInView.length > 5000) {
		recommendations.push('Consider implementing virtualization');
	}
	if (density > 1000) {
		recommendations.push('Reduce cluster minimum size');
	}

	return { strategy, reasoning, recommendations };
}

// Message handler for the Web Worker
self.onmessage = function(e: MessageEvent<ClusteringMessage>) {
	const { type, data, requestId } = e.data;
	const startTime = performance.now();

	try {
		switch (type) {
			case 'CLUSTER_PROPERTIES': {
				const { properties, bounds, zoom, options } = data;
				const clusters = clusterProperties(properties, bounds, zoom, options);
				
				const response: ClusteringResponse = {
					type: 'CLUSTERING_COMPLETE',
					data: clusters,
					requestId,
					processingTime: performance.now() - startTime,
				};
				
				self.postMessage(response);
				break;
			}

			case 'CALCULATE_BOUNDS': {
				const { properties } = data;
				const optimalBounds = calculateOptimalBounds(properties);
				
				const response: ClusteringResponse = {
					type: 'BOUNDS_CALCULATED',
					data: optimalBounds,
					requestId,
					processingTime: performance.now() - startTime,
				};
				
				self.postMessage(response);
				break;
			}

			case 'OPTIMIZE_VIEWPORT': {
				const { properties, bounds, zoom } = data;
				const optimization = optimizeViewportStrategy(properties, bounds, zoom);
				
				const response: ClusteringResponse = {
					type: 'VIEWPORT_OPTIMIZED',
					data: optimization,
					requestId,
					processingTime: performance.now() - startTime,
				};
				
				self.postMessage(response);
				break;
			}

			default:
				throw new Error(`Unknown message type: ${type}`);
		}
	} catch (error) {
		const response: ClusteringResponse = {
			type: 'ERROR',
			data: {
				error: error instanceof Error ? error.message : 'Unknown error',
				stack: error instanceof Error ? error.stack : undefined,
			},
			requestId,
			processingTime: performance.now() - startTime,
		};
		
		self.postMessage(response);
	}
}; 
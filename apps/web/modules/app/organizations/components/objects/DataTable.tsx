"use client";

import BottomBar from "@app/contacts/components/BottomBar";
import { arrSome, inDateRange } from "@app/organizations/lib/filterfns";
import { useCreatePin, useDeletePin, useIsPinned } from "@app/pins/lib/api";
import {
	DataTableCellContextMenu,
	DataTableContextMenu,
} from "@app/shared/components/data-table/DataTableContextMenu";
import { InlineCellEditor } from "@app/shared/components/data-table/InlineCellEditor";
import { STORAGE_KEYS } from "@app/shared/lib/constants";
import { useLocalStorage } from "@app/shared/lib/local-storage";
import { useRouter } from "next/navigation";
import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	KeyboardSensor,
	MouseSensor,
	TouchSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import { restrictToHorizontalAxis } from "@dnd-kit/modifiers";
import {
	arrayMove,
	horizontalListSortingStrategy,
	SortableContext,
} from "@dnd-kit/sortable";
import {
	useMutation,
	useQueryClient,
} from "@tanstack/react-query";
import type {
	ColumnDef,
	ColumnFiltersState,
	Row,
	RowSelectionState,
	SortingState,
	TableOptions,
	Table as TTable,
	VisibilityState,
} from "@tanstack/react-table";
import {
	flexRender,
	getCoreRowModel,
	getFilteredRowModel,
	getSortedRowModel,
	useReactTable,
} from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	Table,
	TableBody,
	TableCell,
	TableFooter,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import { type ParserBuilder, useQueryStates } from "nuqs";
import * as React from "react";
import { toast } from "sonner";
import { AddColumnPopover } from "./AddColumnPopover";
import { DataTableFilterCommand } from "./shared/data-table/data-table-filter-command";
import { DataTableFilterControls } from "./shared/data-table/data-table-filter-controls";
import { DataTableProvider } from "./shared/data-table/data-table-provider";
import type {
	DataTableFilterField,
	SheetField,
} from "./shared/data-table/types";
import PropertySidebar from "@app/organizations/components/PropertySidebar";
import { ArrayFieldType, FieldType, ObjectType } from "@repo/database";
import { HeaderCell } from "./shared/HeaderCell";
import { FooterCell } from "./shared/FooterCell";
import { MemoizedTableRowComponent } from "./shared/TableRowComponent";
import { IconPlus } from "@tabler/icons-react";

export interface DataTableProps<TData, TValue, TMeta> {
	columns: ColumnDef<TData, TValue>[];
	getRowClassName?: (row: Row<TData>) => string;
	getRowId?: TableOptions<TData>["getRowId"];
	data: TData[];
	defaultColumnFilters?: ColumnFiltersState;
	defaultColumnSorting?: SortingState;
	defaultRowSelection?: RowSelectionState;
	defaultColumnVisibility?: VisibilityState;
	filterFields?: DataTableFilterField<TData>[];
	sheetFields?: SheetField<TData, TMeta>[];
	isFetching?: boolean;
	isLoading?: boolean;
	refetch: () => Promise<void>;
	totalRows?: number;
	filterRows?: number;
	chartData?: any[];
	chartDataColumnId?: string;
	getFacetedUniqueValues?: (table: TTable<TData>, columnId: string) => Map<string, number>;
	getFacetedMinMaxValues?: (table: TTable<TData>, columnId: string) => [number, number] | undefined;
	meta?: TMeta;
	onResetFilters?: () => void;
	renderLiveRow?: (row: Row<TData>) => React.ReactNode;
	renderSheetTitle?: (row: Row<TData>) => string;
	renderHeader?: () => React.ReactNode;
	searchParamsParser: Record<string, ParserBuilder<any>>;
	objectType?: string;
	primaryColumn?: string;
	view?: {
		id: string;
		name: string;
		columnDefs: Array<{
			field: string;
			headerName: string;
			width: number;
			type?: string;
		}>;
		updatedAt?: string;
	};
	onUpdateView?: (updatedView: any) => void;
	onEdit?: (e: React.MouseEvent, recordId: string) => void;
	onFavorite?: (e: React.MouseEvent, recordId: string) => void;
	onPin?: (e: React.MouseEvent, recordId: string, record: any, isPinned: boolean) => void;
	onDelete?: (e: React.MouseEvent, recordId: string) => void;
	onCopy?: (e: React.MouseEvent, value: string) => void;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
	onEditCell?: (
		e: React.MouseEvent,
		cell: { columnId: string; value: any; rowId: string },
	) => Promise<void>;
	onClearValue?: (
		e: React.MouseEvent,
		cell: { columnId: string; rowId: string },
	) => Promise<void>;
	customMenuItems?: (record: any) => any[];
	isFavorite?: (record: any) => boolean;
	organizationId?: string;
	fieldTypes?: Record<string, FieldType>;
	arrayFieldTypes?: Record<string, ArrayFieldType>;
	selectOptions?: Record<string, Array<{ label: string; value: string }>>;
	enableInlineEditing?: boolean;
	readonlyColumns?: string[];
	renderFilterBar?: boolean;
	renderFilterControls?: boolean;
	selectedProperty?: any;
	onPropertySelect?: (property: any) => void;
	onRowSelectionChange?: (selection: RowSelectionState) => void;
	// Pagination mode props
	isPaginationMode?: boolean;
	paginationInfo?: {
		currentPage: number;
		totalPages: number;
		pageSize: number;
		total: number;
		offset: number;
	};
	onPageChange?: (page: number) => void;
}

export function DataTable<TData, TValue, TMeta>({
	columns,
	getRowClassName,
	getRowId,
	data,
	defaultColumnFilters = [],
	defaultColumnSorting = [],
	defaultRowSelection = {},
	defaultColumnVisibility = {},
	filterFields = [],
	sheetFields = [],
	isFetching,
	isLoading,
	refetch,
	totalRows = 0,
	filterRows = 0,
	chartData = [],
	chartDataColumnId,
	getFacetedUniqueValues,
	getFacetedMinMaxValues,
	meta,
	renderLiveRow,
	renderSheetTitle,
	renderHeader,
	searchParamsParser,
	objectType,
	primaryColumn = "name",
	view,
	onUpdateView,
	onEdit,
	onFavorite,
	onPin,
	onDelete,
	onCopy,
	onHide,
	onEditCell,
	onClearValue,
	customMenuItems,
	isFavorite,
	organizationId,
	fieldTypes = {},
	arrayFieldTypes = {},
	selectOptions = {},
	enableInlineEditing = true,
	readonlyColumns = [],
	renderFilterBar = true,
	renderFilterControls = true,
	selectedProperty,
	onPropertySelect,
	onRowSelectionChange,
	isPaginationMode = false,
	paginationInfo,
	onPageChange,
	onResetFilters,
}: DataTableProps<TData, TValue, TMeta>) {
	// URL state management - always use useQueryStates for filtering to work
	const [search, setSearch] = useQueryStates(searchParamsParser);

	// Create a reset function that clears URL filters
	const handleResetFilters = React.useCallback(() => {
		if (onResetFilters) {
			onResetFilters();
		} else {
			// Default behavior: clear all filters from URL while preserving system params
			const { sort, start, size, id, cursor, direction, live, ...filters } = search;
			const newSearch: Record<string, any> = {};
			
			if (sort) newSearch.sort = sort;
			if (size) newSearch.size = size;
			if (id) newSearch.id = id;
			if (cursor) newSearch.cursor = cursor;
			if (direction) newSearch.direction = direction;
			if (live) newSearch.live = live;
			
			// Reset pagination if in pagination mode
			if (isPaginationMode) {
				newSearch.start = 0;
			}
			
			// Only reset filter fields that are actually defined in the searchParamsParser
			// This prevents trying to set undefined for fields that don't exist in the schema
			const validFilterKeys = Object.keys(searchParamsParser).filter(key => 
				key !== 'sort' && key !== 'start' && key !== 'size' && 
				key !== 'id' && key !== 'cursor' && key !== 'direction' && key !== 'live'
			);
			
			// Reset ALL valid filter fields to null, not just ones that currently have values
			validFilterKeys.forEach(filterKey => {
				newSearch[filterKey] = null; // Use null instead of undefined for proper nuqs serialization
			});
			
			setSearch(newSearch);
		}
	}, [onResetFilters, search, setSearch, isPaginationMode, searchParamsParser]);

	// Extract filters from search params for display
	const { sort, start, size, id, cursor, direction, live } = search;
	const activeFilters = React.useMemo(() => {
		const { sort, start, size, id, cursor, direction, live, ...filters } = search;
		return filters;
	}, [search]);

	// Convert URL search params to TanStack Table filter format
	const urlColumnFilters = React.useMemo(() => {
		console.log("Recalculating urlColumnFilters, activeFilters:", activeFilters);
		
		// For pagination mode, merge URL filters with defaults
		const urlFilters = Object.entries(activeFilters)
			.filter(([_, value]) => value !== null && value !== undefined && value !== "")
			.map(([key, value]) => ({
				id: key,
				value,
			}));

		console.log("URL filters from activeFilters:", urlFilters);

		if (isPaginationMode) {
			// In pagination mode, merge URL filters with default filters
			const merged = [...defaultColumnFilters];
			urlFilters.forEach((urlFilter) => {
				const existingIndex = merged.findIndex(f => f.id === urlFilter.id);
				if (existingIndex >= 0) {
					merged[existingIndex] = urlFilter;
				} else {
					merged.push(urlFilter);
				}
			});
			console.log("Final merged urlColumnFilters:", merged);
			return merged;
		}
		
		console.log("Final urlColumnFilters (no pagination mode):", urlFilters);
		return urlFilters;
	}, [isPaginationMode, defaultColumnFilters, activeFilters]);

	// Local UI state
	const [sorting, setSorting] = React.useState<SortingState>(
		sort ? [{ id: sort.id, desc: sort.desc }] : defaultColumnSorting
	);
	const [rowSelection, setRowSelection] = React.useState<RowSelectionState>(defaultRowSelection);
	const [columnOrder, setColumnOrder] = useLocalStorage<string[]>(
		`${STORAGE_KEYS.DATA_TABLE_COLUMN_ORDER}-${objectType || "default"}-${view?.id || "default"}`,
		[],
	);
	const [columnVisibility, setColumnVisibility] = useLocalStorage<VisibilityState>(
		`${STORAGE_KEYS.DATA_TABLE_COLUMN_VISIBILITY}-${objectType || "default"}-${view?.id || "default"}`,
		defaultColumnVisibility,
	);
	const [columnSizing, setColumnSizing] = useLocalStorage<Record<string, number>>(
		`${STORAGE_KEYS.DATA_TABLE_COLUMN_SIZING}-${objectType || "default"}-${view?.id || "default"}`,
		{ select: 40 },
	);
	const [columnAggregations, setColumnAggregations] = useLocalStorage<Record<string, string>>(
		`${STORAGE_KEYS.DATA_TABLE_COLUMN_AGGREGATIONS}-${objectType || "default"}-${view?.id || "default"}`,
		{},
	);

	const [editingCell, setEditingCell] = React.useState<{
		rowId: string;
		columnId: string;
	} | null>(null);
	const [selectedCell, setSelectedCell] = React.useState<{
		rowId: string;
		columnId: string;
	} | null>(null);
	const [showBottomBar, setShowBottomBar] = React.useState(false);
	const [topBarHeight, setTopBarHeight] = React.useState(0);

	const topBarRef = React.useRef<HTMLDivElement>(null);
	const tableRef = React.useRef<HTMLTableElement>(null);

	// Query client for view updates
	const queryClient = useQueryClient();

	// Pin functionality
	const createPinMutation = useCreatePin(organizationId);
	const deletePinMutation = useDeletePin(organizationId);

	// Ensure select column is always 40px
	const ensureSelectColumnSizing = React.useCallback(
		(sizing: Record<string, number>) => {
			return { ...sizing, select: 40 };
		},
		[],
	);

	const safeColumnSizing = React.useMemo(() => {
		return ensureSelectColumnSizing(columnSizing);
	}, [columnSizing, ensureSelectColumnSizing]);

	// Debug: Monitor when column filters change
	React.useEffect(() => {
		console.log("Table column filters changed to:", urlColumnFilters);
	}, [urlColumnFilters]);

	// Create table instance
	const table = useReactTable({
		data,
		columns,
		state: {
			columnFilters: urlColumnFilters,
			sorting,
			columnVisibility,
			rowSelection,
			columnOrder,
			columnSizing: safeColumnSizing,
			columnPinning: {
				left: ["select", primaryColumn],
			},
		},
		enableMultiRowSelection: true,
		columnResizeMode: "onChange",
		columnResizeDirection: "ltr",
		enableColumnResizing: true,
		getRowId,
		enableColumnPinning: true,
		manualFiltering: true,
		onColumnVisibilityChange: setColumnVisibility,
		onColumnFiltersChange: (updater) => {
			const newFilters = typeof updater === 'function' ? updater(urlColumnFilters) : updater;
			const newSearchParams: Record<string, any> = {};
			
			const { sort, start, size, id, cursor, direction, live } = search;
			if (sort) newSearchParams.sort = sort;
			if (size) newSearchParams.size = size;
			if (id) newSearchParams.id = id;
			if (cursor) newSearchParams.cursor = cursor;
			if (direction) newSearchParams.direction = direction;
			if (live) newSearchParams.live = live;
			
			newFilters.forEach((filter) => {
				if (filter.value !== null && filter.value !== undefined && filter.value !== "") {
					if (Array.isArray(filter.value)) {
						newSearchParams[filter.id] = filter.value;
					} else if (typeof filter.value === 'object' && filter.value !== null && 'from' in filter.value && 'to' in filter.value) {
						newSearchParams[filter.id] = [(filter.value as any).from, (filter.value as any).to];
					} else {
						newSearchParams[filter.id] = filter.value;
					}
				}
			});

			// Reset pagination when filters change
			if (isPaginationMode) {
				newSearchParams.start = 0;
			}

			setSearch(newSearchParams);
		},
		onRowSelectionChange: (updater) => {
			const newSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
			setRowSelection(newSelection);
			onRowSelectionChange?.(newSelection);
		},
		onSortingChange: (updater) => {
			const newSorting = typeof updater === 'function' ? updater(sorting) : updater;
			setSorting(newSorting);
			
			const sortParam = newSorting[0] ? {
				id: newSorting[0].id,
				desc: newSorting[0].desc
			} : null;
			
			const searchUpdate: Record<string, any> = { sort: sortParam };
			// Reset pagination when sorting changes
			if (isPaginationMode) {
				searchUpdate.start = 0;
			}
			
			setSearch(searchUpdate);
		},
		onColumnOrderChange: setColumnOrder,
		onColumnSizingChange: (sizing) => {
			const currentSizing = safeColumnSizing;
			const newSizing = typeof sizing === "function" ? sizing(currentSizing) : sizing;
			const finalSizing = ensureSelectColumnSizing(newSizing);
			setColumnSizing(finalSizing);
		},
		getSortedRowModel: getSortedRowModel(),
		getCoreRowModel: getCoreRowModel(),
		filterFns: { inDateRange, arrSome },
		debugAll: process.env.NEXT_PUBLIC_TABLE_DEBUG === "true",
		meta: { 
			getRowClassName,
			totalRows,
			filterRows,
		},
	});

	// Initialize and sync column order
	React.useEffect(() => {
		const allColumnIds = table.getAllColumns().map((col) => col.id);

		if (
			columnOrder.length === 0 ||
			columnOrder.length !== allColumnIds.length ||
			!columnOrder.every((id) => allColumnIds.includes(id))
		) {
			setColumnOrder(allColumnIds);
		}
	}, [table, columnOrder, setColumnOrder]);

	// Global click handler to unfocus cells when clicking outside
	React.useEffect(() => {
		const handleGlobalClick = (e: MouseEvent) => {
			if (tableRef.current && !tableRef.current.contains(e.target as Node)) {
				if (editingCell) {
					setEditingCell(null);
				}
				if (selectedCell) {
					setSelectedCell(null);
				}
			}
		};

		document.addEventListener('mousedown', handleGlobalClick);
		return () => {
			document.removeEventListener('mousedown', handleGlobalClick);
		};
	}, [editingCell, selectedCell]);

	// Mutation to update view column order
	const updateViewColumnOrderMutation = useMutation({
		mutationFn: async (updatedColumnDefs: any[]) => {
			if (!view?.id) return;

			const response = await fetch(`/api/object-views/views/${view.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					columnDefs: updatedColumnDefs,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to update view");
			}

			return response.json();
		},
		onSuccess: (data) => {
			if (organizationId) {
				queryClient.invalidateQueries({
					queryKey: ["objectViews", organizationId, objectType],
				});
			}
			if (view?.id) {
				queryClient.invalidateQueries({
					queryKey: ["objectView", view.id],
				});
			}

			if (onUpdateView && data) {
				onUpdateView(data);
			}
		},
		onError: (error) => {
			console.error("Failed to update view column order:", error);
			toast.error("Failed to update view");
		},
	});

	// Mutation specifically for hiding columns
	const hideColumnMutation = useMutation({
		mutationFn: async ({
			updatedColumnDefs,
			columnName,
		}: {
			updatedColumnDefs: any[];
			columnName: string;
		}) => {
			if (!view?.id) return { data: null, columnName };

			const response = await fetch(`/api/object-views/views/${view.id}`, {
				method: "PATCH",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					columnDefs: updatedColumnDefs,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to hide column");
			}

			const data = await response.json();
			return { data, columnName };
		},
		onSuccess: (result) => {
			if (!result) return;

			const { columnName, data } = result;

			if (organizationId) {
				queryClient.invalidateQueries({
					queryKey: ["objectViews", organizationId, objectType],
				});
			}
			if (view?.id) {
				queryClient.invalidateQueries({
					queryKey: ["objectView", view.id],
				});
			}

			if (onUpdateView && data) {
				onUpdateView(data);
			}
		},
		onError: (error) => {
			console.error("Failed to hide column:", error);
			toast.error("Failed to hide column");
		},
	});

	// Handle hiding columns from the header dropdown
	const handleHideColumn = React.useCallback(
		(e: React.MouseEvent, columnId: string) => {
			if (!view?.columnDefs) return;

			if (columnId === primaryColumn) {
				console.warn("Cannot hide primary column:", primaryColumn);
				return;
			}

			const columnToHide = view.columnDefs.find(
				(col) => col.field === columnId,
			);
			const columnName = columnToHide?.headerName || columnId;

			const updatedColumnDefs = view.columnDefs.filter(
				(col) => col.field !== columnId,
			);
			hideColumnMutation.mutate({ updatedColumnDefs, columnName });

			if (onHide) {
				onHide(e, columnId);
			}
		},
		[view, primaryColumn, hideColumnMutation, onHide],
	);

	const handlePin = React.useCallback(
		(
			e: React.MouseEvent,
			recordId: string,
			record: any,
			isPinned: boolean,
		) => {
			if (!organizationId || !objectType) return;

			if (isPinned) {
				deletePinMutation.mutate({
					objectId: recordId,
					objectType: objectType as ObjectType,
					organizationId,
				});
			} else {
				const displayName =
					record[primaryColumn] ||
					record.name ||
					record.title ||
					`${objectType} ${recordId}`;

				createPinMutation.mutate({
					objectId: recordId,
					objectType: objectType as ObjectType,
					name: displayName,
					organizationId,
				});
			}
		},
		[
			organizationId,
			objectType,
			primaryColumn,
			createPinMutation,
			deletePinMutation,
		],
	);

	// Sync column order and visibility with view
	React.useEffect(() => {
		if (view?.columnDefs && view.columnDefs.length > 0) {
			const viewColumnOrder = view.columnDefs.map((col) => col.field);
			const allColumnIds = table.getAllColumns().map((col) => col.id);

			const actualPrimaryColumn =
				allColumnIds.find((id) => {
					const column = table.getColumn(id);
					return (
						id === primaryColumn ||
						column?.columnDef.id === primaryColumn ||
						(column?.columnDef as any)?.accessorKey ===
							primaryColumn
					);
				}) || primaryColumn;

			const newVisibility: VisibilityState = {};
			allColumnIds.forEach((columnId) => {
				const viewColumn = view.columnDefs.find(
					(col) => col.field === columnId,
				);
				if (
					columnId === "select" ||
					columnId === primaryColumn ||
					columnId === actualPrimaryColumn
				) {
					newVisibility[columnId] = true;
				} else if (viewColumn) {
					newVisibility[columnId] = true;
				} else {
					newVisibility[columnId] = false;
				}
			});

			if (
				JSON.stringify(newVisibility) !==
				JSON.stringify(columnVisibility)
			) {
				setColumnVisibility(newVisibility);
			}

			const pinnedColumns = ["select", actualPrimaryColumn];
			const nonPinnedViewOrder = viewColumnOrder.filter(
				(id) => !pinnedColumns.includes(id),
			);
			const nonPinnedAllColumns = allColumnIds.filter(
				(id) => !pinnedColumns.includes(id),
			);

			const syncedOrder = [
				...pinnedColumns.filter((id) => allColumnIds.includes(id)),
				...nonPinnedViewOrder.filter((id) => allColumnIds.includes(id)),
				...nonPinnedAllColumns.filter(
					(id) => !viewColumnOrder.includes(id),
				),
			];

			if (JSON.stringify(syncedOrder) !== JSON.stringify(columnOrder)) {
				setColumnOrder(syncedOrder);
			}
		}
	}, [view?.columnDefs, view?.updatedAt, view?.id, table, primaryColumn]);

	// Sync sorting state with URL on mount
	React.useEffect(() => {
		if (sort && sorting.length === 0) {
			setSorting([{ id: sort.id, desc: sort.desc }]);
		}
	}, [sort]);

	const selectedRow = React.useMemo(() => {
		if ((isLoading || isFetching) && !data.length) return;
		const selectedRowKey = Object.keys(rowSelection)?.[0];
		return table
			.getCoreRowModel()
			.flatRows.find((row) => row.id === selectedRowKey);
	}, [rowSelection, table, isLoading, isFetching, data]);

	const selectedRows = React.useMemo(() => {
		return table.getSelectedRowModel().rows;
	}, [table, rowSelection]);

	React.useEffect(() => {
		setShowBottomBar(selectedRows.length > 0);
	}, [selectedRows]);

	React.useEffect(() => {
		if (isLoading || isFetching) return;
		if (Object.keys(rowSelection)?.length && !selectedRow) {
			setRowSelection({});
		} else if (Object.keys(rowSelection)?.length) {
			// Will do when we include quick view
		}
	}, [rowSelection, selectedRow, isLoading, isFetching]);

	const columnSizeVars = React.useMemo(() => {
		const headers = table.getFlatHeaders();
		const colSizes: { [key: string]: string } = {};
		for (let i = 0; i < headers.length; i++) {
			const header = headers[i]!;
			const size =
				header.id === "select" || header.column.id === "select"
					? 40
					: header.getSize();
			const columnSize =
				header.id === "select" || header.column.id === "select"
					? 40
					: header.column.getSize();

			colSizes[`--header-${header.id.replace(".", "-")}-size`] =
				`${size}px`;
			colSizes[`--col-${header.column.id.replace(".", "-")}-size`] =
				`${columnSize}px`;
		}
		return colSizes;
	}, [
		table.getState().columnSizingInfo,
		table.getState().columnSizing,
		table.getState().columnVisibility,
	]);

	const currentColumnOrder = table.getState().columnOrder;

	// Check if only pinned columns are visible
	const visibleColumns = table.getVisibleLeafColumns();
	const onlyPinnedColumnsVisible = React.useMemo(() => {
		const visibleColumnIds = visibleColumns.map((col) => col.id);

		const actualPrimaryColumn = visibleColumnIds.find((id) => {
			const column = table.getColumn(id);
			return (
				id === primaryColumn ||
				column?.columnDef.id === primaryColumn ||
				(column?.columnDef as any)?.accessorKey === primaryColumn
			);
		});

		const systemColumns = ["select", actualPrimaryColumn].filter(Boolean);
		const nonSystemColumns = visibleColumnIds.filter(
			(id) => !systemColumns.includes(id),
		);

		return nonSystemColumns.length === 0 && visibleColumnIds.length > 0;
	}, [visibleColumns, primaryColumn, table]);

	// Set up sensors for drag and drop
	const sensors = useSensors(
		useSensor(MouseSensor, {
			activationConstraint: {
				delay: 200,
				tolerance: 5,
			},
		}),
		useSensor(TouchSensor, {
			activationConstraint: {
				delay: 200,
				tolerance: 5,
			},
		}),
		useSensor(KeyboardSensor),
	);

	const [dragOverId, setDragOverId] = React.useState<string | null>(null);

	const handleDragStart = React.useCallback((event: any) => {
		// Optional: Add any drag start logic here
	}, []);

	const handleDragOver = React.useCallback((event: any) => {
		const { over } = event;
		setDragOverId(over?.id || null);
	}, []);

	const handleDragEnd = React.useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;

			setDragOverId(null);

			if (active.id !== over?.id && over) {
				setColumnOrder((columnOrder) => {
					const oldIndex = columnOrder.indexOf(active.id as string);
					const newIndex = columnOrder.indexOf(over.id as string);
					const newColumnOrder = arrayMove(
						columnOrder,
						oldIndex,
						newIndex,
					);

					if (view?.columnDefs && view.columnDefs.length > 0) {
						const columnDefsMap = new Map(
							view.columnDefs.map((col) => [col.field, col]),
						);

						const reorderedColumnDefs = newColumnOrder
							.map((fieldId) => columnDefsMap.get(fieldId))
							.filter(
								(col): col is NonNullable<typeof col> =>
									col !== undefined,
							);

						const orderedFields = new Set(newColumnOrder);
						const remainingColumnDefs = view.columnDefs.filter(
							(col) => !orderedFields.has(col.field),
						);

						const finalColumnDefs = [
							...reorderedColumnDefs,
							...remainingColumnDefs,
						];
						updateViewColumnOrderMutation.mutate(finalColumnDefs);
					}

					return newColumnOrder;
				});
			}
		},
		[setColumnOrder, columnOrder, view, updateViewColumnOrderMutation],
	);

	// Helper functions for aggregations
	const calculateAggregation = React.useCallback(
		(columnId: string, aggregationType: string) => {
			switch (aggregationType) {
				case "count":
					return filterRows.toString();
				case "count-empty": {
					const rows = table.getCoreRowModel().rows;
					const emptyCount = rows.filter((row) => {
						const value = row.getValue(columnId);
						return (
							value === null ||
							value === undefined ||
							value === "" ||
							(Array.isArray(value) && value.length === 0)
						);
					}).length;
					return emptyCount.toString();
				}
				case "count-filled": {
					const rows = table.getCoreRowModel().rows;
					const filledCount = rows.filter((row) => {
						const value = row.getValue(columnId);
						return (
							value !== null &&
							value !== undefined &&
							value !== "" &&
							(!Array.isArray(value) || value.length > 0)
						);
					}).length;
					return filledCount.toString();
				}
				case "percent-empty": {
					if (filterRows === 0) return "0%";
					const rows = table.getCoreRowModel().rows;
					const emptyCount = rows.filter((row) => {
						const value = row.getValue(columnId);
						return (
							value === null ||
							value === undefined ||
							value === "" ||
							(Array.isArray(value) && value.length === 0)
						);
					}).length;
					return `${Math.round((emptyCount / filterRows) * 100)}%`;
				}
				case "percent-filled": {
					if (filterRows === 0) return "0%";
					const rows = table.getCoreRowModel().rows;
					const filledCount = rows.filter((row) => {
						const value = row.getValue(columnId);
						return (
							value !== null &&
							value !== undefined &&
							value !== "" &&
							(!Array.isArray(value) || value.length > 0)
						);
					}).length;
					return `${Math.round((filledCount / filterRows) * 100)}%`;
				}
				default:
					return "0";
			}
		},
		[table, filterRows],
	);

	const getAggregationLabel = (aggregationType: string) => {
		switch (aggregationType) {
			case "count":
				return "count";
			case "count-empty":
				return "count empty";
			case "count-filled":
				return "count filled";
			case "percent-empty":
				return "percent empty";
			case "percent-filled":
				return "percent filled";
			default:
				return aggregationType;
		}
	};

	const setAggregation = (columnId: string, aggregationType: string) => {
		setColumnAggregations((prev) => ({
			...prev,
			[columnId]: aggregationType,
		}));
	};

	const removeAggregation = (columnId: string) => {
		setColumnAggregations((prev) => {
			const newAggregations = { ...prev };
			delete newAggregations[columnId];
			return newAggregations;
		});
	};

	React.useEffect(() => {
		const observer = new ResizeObserver(() => {
			const rect = topBarRef.current?.getBoundingClientRect();
			if (rect) {
				setTopBarHeight(rect.height);
			}
		});

		const topBar = topBarRef.current;
		if (!topBar) return;

		observer.observe(topBar);
		return () => observer.unobserve(topBar);
	}, [topBarRef]);

	return (
		<DataTableProvider
			table={table}
			columns={columns}
			filterFields={filterFields}
			columnFilters={urlColumnFilters}
			sorting={sorting}
			rowSelection={rowSelection}
			columnOrder={columnOrder}
			columnVisibility={columnVisibility}
			enableColumnOrdering={true}
			isLoading={isFetching || isLoading}
			getFacetedUniqueValues={getFacetedUniqueValues}
			getFacetedMinMaxValues={getFacetedMinMaxValues}
			onResetFilters={handleResetFilters}
		>
			{renderHeader?.()}
			<div
				className="flex h-[calc(100vh-100px)] w-full flex-col sm:flex-row overflow-hidden"
				style={
					{
						"--top-bar-height": `${topBarHeight}px`,
						...columnSizeVars,
					} as React.CSSProperties
				}
			>
				{renderFilterControls && (
				<div
					className={cn(
						"flex w-full flex-col sm:sticky sm:top-0 sm:h-screen sm:min-w-52 sm:max-w-52 sm:self-start md:min-w-72 md:max-w-72",
						"group-data-[expanded=false]/controls:hidden",
						"hidden sm:flex",
					)}
				>
					<DataTableFilterControls />
				</div>
				)}
				<div
					className={cn(
						"flex max-w-full flex-1 flex-col border-border sm:border-l h-full overflow-hidden",
						"group-data-[expanded=true]/controls:sm:max-w-[calc(100vw_-_208px)]",
						renderFilterBar ? "group-data-[expanded=true]/controls:md:max-w-[calc(100vw_-_288px)]" : "",
					)}
				>
					{renderFilterBar && (
						<div
							ref={topBarRef}
							className={cn(
								"flex flex-col gap-4 bg-sidebar p-2 flex-shrink-0",
								"sticky top-0 z-50",
							)}
						>
							<DataTableFilterCommand
								searchParamsParser={searchParamsParser}
							/>
						</div>
					)}
					<div className="flex-1 overflow-hidden relative flex flex-col">
						<div className="flex-1 relative">
							<div className="absolute inset-0 overflow-auto">
								<DndContext
									sensors={sensors}
									collisionDetection={closestCenter}
									onDragStart={handleDragStart}
									onDragOver={handleDragOver}
									onDragEnd={handleDragEnd}
									modifiers={[restrictToHorizontalAxis]}
								>
									<Table
										ref={tableRef}
										className="border-separate border-spacing-0 w-full [&_th:first-child]:!w-10 [&_th:first-child]:!min-w-10 [&_th:first-child]:!max-w-10"
										style={{
											tableLayout: "fixed",
										}}
									>
										<style
											dangerouslySetInnerHTML={{
												__html: `
	                      table .select-column-header,
	                      table .select-column-cell,
	                      table th:first-child,
	                      table td:first-child {
	                        width: 40px !important;
	                        min-width: 40px !important;
	                        max-width: 40px !important;
	                        flex: 0 0 40px !important;
	                        box-sizing: border-box !important;
	                        overflow: hidden !important;
	                      }
	                    `,
											}}
										/>
										<TableHeader
											className={cn(
												"sticky top-0 bg-sidebar",
												"shadow-sm border-b border-border",
											)}
										>
											{table
												.getHeaderGroups()
												.map((headerGroup) => (
													<TableRow
														key={headerGroup.id}
														className={cn(
															"h-8",
															"bg-sidebar",
															"[&>:not(:last-child)]:border-r",
														)}
													>
														<SortableContext
															items={columnOrder}
															strategy={
																horizontalListSortingStrategy
															}
														>
															{headerGroup.headers.map(
																(header) => (
																	<HeaderCell
																		key={
																			header.id
																		}
																		header={
																			header
																		}
																		primaryColumn={
																			primaryColumn
																		}
																		readonlyColumns={
																			readonlyColumns
																		}
																		dragOverId={
																			dragOverId
																		}
																		onHide={
																			handleHideColumn
																		}
																		onMoveColumn={(
																			id,
																			direction,
																		) => {
																			const currentOrder =
																				[
																					...table.getState()
																						.columnOrder,
																				];
																			const columnIndex =
																				currentOrder.indexOf(
																					id,
																				);

																			if (
																				columnIndex >
																					0 &&
																				direction ===
																					"left"
																			) {
																				const newOrder =
																					[
																						...currentOrder,
																					];
																				[
																					newOrder[
																						columnIndex -
																							1
																					],
																					newOrder[
																						columnIndex
																					],
																				] =
																					[
																						newOrder[
																							columnIndex
																						],
																						newOrder[
																							columnIndex -
																								1
																						],
																					];
																				table.setColumnOrder(
																					newOrder,
																				);
																			} else if (
																				columnIndex <
																					currentOrder.length -
																						1 &&
																				direction ===
																					"right"
																			) {
																				const newOrder =
																					[
																						...currentOrder,
																					];
																				[
																					newOrder[
																						columnIndex
																					],
																					newOrder[
																						columnIndex +
																							1
																					],
																				] =
																					[
																						newOrder[
																							columnIndex +
																								1
																						],
																						newOrder[
																							columnIndex
																						],
																					];
																				table.setColumnOrder(
																					newOrder,
																				);
																			}
																		}}
																	/>
																),
															)}
														</SortableContext>
														{/* Add column header */}
														<TableHead className="border-t border-b border-border p-0 min-w-[300px] w-full">
															<div className="flex items-center justify-start h-10 px-2 min-w-[300px] w-full">
																<AddColumnPopover
																	view={view}
																	objectType={objectType as ObjectType}
																	primaryColumn={
																		primaryColumn
																	}
																	onColumnAdded={() => {
																		refetch();
																	}}
																>
																	<Button
																		variant="ghost"
																		className="hover:bg-muted/50 text-muted-foreground rounded-lg h-8 px-3"
																	>
																		<div className="flex items-center justify-center gap-2">
																			<IconPlus className="w-4 h-4" />
																			<span>
																				Add
																				column
																			</span>
																		</div>
																	</Button>
																</AddColumnPopover>
															</div>
														</TableHead>
													</TableRow>
												))}
										</TableHeader>
										<TableBody id="content" tabIndex={-1}>
											{table.getRowModel().rows
												?.length ? (
												table
													.getRowModel()
													.rows.map((row) => {
														if (
															onlyPinnedColumnsVisible
														) {
															// Custom row rendering for empty state
															return (
																<TableRow
																	key={row.id}
																	className="h-10 group [&>:not(:last-child)]:border-r"
																>
																	{/* Render existing row cells */}
																	{row
																		.getVisibleCells()
																		.map(
																			(
																				cell,
																			) => {
																				const columnAccessorKey =
																					(
																						cell
																							.column
																							.columnDef as any
																					)
																						?.accessorKey;
																				const isPrimaryOrSelectColumn =
																					cell
																						.column
																						.id ===
																						"select" ||
																					cell
																						.column
																						.id ===
																						primaryColumn ||
																					columnAccessorKey ===
																						primaryColumn;

																				return (
																					<TableCell
																						key={
																							cell.id
																						}
																						style={{
																							width:
																								cell
																									.column
																									.id ===
																								"select"
																									? 40
																									: cell.column.getSize(),
																							minWidth:
																								cell
																									.column
																									.id ===
																								"select"
																									? 40
																									: cell.column.getSize(),
																							maxWidth:
																								cell
																									.column
																									.id ===
																								"select"
																									? 40
																									: cell.column.getSize(),
																							...(cell
																								.column
																								.id ===
																								primaryColumn && {
																								left: "40px",
																							}),
																						}}
																						className={cn(
																							"border-b border-border relative align-middle min-w-0",
																							cell
																								.column
																								.id ===
																								"select"
																								? "select-column-cell !p-0 !w-10 !min-w-10 !max-w-10"
																								: "p-2",
																							cell.column.getIsPinned() && [
																								"sticky z-10",
																								"bg-background/55 backdrop-blur-3xl supports-[backdrop-filter]:bg-background/35",
																								cell
																									.column
																									.id ===
																									primaryColumn &&
																									"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]",
																								cell
																									.column
																									.id ===
																								"select"
																									? "left-0 shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]"
																									: "",
																							],
																						)}
																					>
																						{cell
																							.column
																							.id ===
																						"select" ? (
																							<div className="flex items-center justify-center w-full h-full">
																								{flexRender(
																									cell
																										.column
																										.columnDef
																										.cell,
																									cell.getContext(),
																								)}
																							</div>
																						) : (
																							<div className="w-full min-w-0 truncate">
																								{flexRender(
																									cell
																										.column
																										.columnDef
																										.cell,
																									cell.getContext(),
																								)}
																							</div>
																						)}
																					</TableCell>
																				);
																			},
																		)}
																	<TableCell className="w-full min-w-0 truncate">
																		{row.index ===
																			0 && (
																			<div className="flex items-center justify-center h-full">
																				<span className="text-xs text-muted-foreground">
																					No
																					columns
																					to
																					display
																				</span>
																			</div>
																		)}
																	</TableCell>
																</TableRow>
															);
														}
														// Normal row rendering
														return (
															<MemoizedTableRowComponent
																key={row.id}
																row={row}
																table={table}
																selected={row.getIsSelected()}
																columnOrder={
																	columnOrder
																}
																primaryColumn={
																	primaryColumn
																}
																onEdit={onEdit}
																onFavorite={
																	onFavorite
																}
																onPin={
																	handlePin
																}
																onDelete={
																	onDelete
																}
																onCopy={onCopy}
																onHide={
																	handleHideColumn
																}
																onEditCell={
																	onEditCell
																}
																onClearValue={
																	onClearValue
																}
																customMenuItems={
																	customMenuItems
																}
																isFavorite={
																	isFavorite
																}
																organizationId={
																	organizationId
																}
																objectType={
																	objectType
																}
																fieldTypes={
																	fieldTypes
																}
																arrayFieldTypes={
																	arrayFieldTypes
																}
																selectOptions={
																	selectOptions
																}
																enableInlineEditing={
																	enableInlineEditing
																}
																editingCell={
																	editingCell
																}
																setEditingCell={
																	setEditingCell
																}
																selectedCell={
																	selectedCell
																}
																setSelectedCell={
																	setSelectedCell
																}
																view={view}
																readonlyColumns={
																	readonlyColumns
																}
																onPropertySelect={
																	onPropertySelect
																}
															/>
														);
													})
											) : (
												<TableRow>
													{onlyPinnedColumnsVisible ? (
														<>
															<TableCell
																className="select-column-cell h-24 border-b border-border bg-background/55 sticky left-0 z-10 !w-10 !min-w-10 !max-w-10 !p-0 !px-0 shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]"
																style={{
																	width: 40,
																	minWidth: 40,
																	maxWidth: 40,
																}}
															>
																{/* Empty select column */}
															</TableCell>
															<TableCell
																className="h-10 border-b border-border bg-background/55 sticky z-10 min-w-0 shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]"
																style={{
																	left: "40px",
																	minWidth:
																		"250px",
																}}
															>
																<div className="text-muted-foreground text-sm truncate">
																	No results.
																</div>
															</TableCell>
														</>
													) : (
														<TableCell colSpan={columns.length + 1} className="h-24 text-center">
															No results.
														</TableCell>
													)}
												</TableRow>
											)}
										</TableBody>
									</Table>
								</DndContext>
							</div>
						</div>

						{/* Footer moved outside scrollable area and pinned to bottom */}
						{renderFilterBar && (
							<div className="border-t border-border bg-background/98 backdrop-blur-xl shadow-xl ring-1 ring-border/50 z-30">
								<div className="overflow-x-auto">
									<Table
										className="border-separate border-spacing-0 relative w-full [&_th:first-child]:!w-10 [&_th:first-child]:!min-w-10 [&_th:first-child]:!max-w-10 [&_td:first-child]:!w-10 [&_td:first-child]:!min-w-10 [&_td:first-child]:!max-w-10"
										style={{
											tableLayout: "fixed",
										}}
									>
										<TableFooter>
											{table
												.getFooterGroups()
												.map((footerGroup) => (
													<TableRow
														key={footerGroup.id}
														className="hover:bg-transparent"
													>
														{footerGroup.headers.map(
															(header) => (
																<FooterCell
																	key={header.id}
																	header={header}
																	primaryColumn={primaryColumn}
																	columnAggregations={columnAggregations}
																	calculateAggregation={calculateAggregation}
																	getAggregationLabel={getAggregationLabel}
																	setAggregation={setAggregation}
																	removeAggregation={removeAggregation}
																	hasNoRecords={data.length === 0}
																	isPaginationMode={
																		isPaginationMode
																	}
																	paginationInfo={
																		paginationInfo
																	}
																	onPageChange={
																		onPageChange
																	}
																	isLoading={
																		isLoading
																	}
																	isFetching={
																		isFetching
																	}
																	objectType={
																		objectType
																	}
																	hasActiveFilters={
																		urlColumnFilters.length > 0
																	}
																/>
															),
														)}
														{/* Add column footer cell */}
														<TableCell
															className={cn(
																"bg-sidebar backdrop-blur-3xl supports-[backdrop-filter]:bg-sidebar min-w-48 min-h-10 py-2",
																data.length ===
																	0 &&
																	"border-t",
															)}
														/>
													</TableRow>
												))}
										</TableFooter>
									</Table>
								</div>
							</div>
						)}
					</div>
					{selectedProperty && (
						<PropertySidebar
							selectedProperty={selectedProperty}
							onClose={() => onPropertySelect?.(null)}
							isTableOpen={true}
						/>
					)}
				</div>
			</div>

			{showBottomBar && (
				<BottomBar
					selectedRecords={selectedRows.map((row) => row.original)}
					objectType={objectType}
				/>
			)}
		</DataTableProvider>
	);
} 
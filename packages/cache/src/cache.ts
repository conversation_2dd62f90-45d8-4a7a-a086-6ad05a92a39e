import { createClient, type RedisClientType } from "redis";

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string;
}

interface CacheConfig {
  url?: string;
  host?: string;
  port?: number;
  password?: string;
  defaultTtl?: number;
  prefix?: string;
}

class CacheService {
  private client: RedisClientType | null = null;
  private config: CacheConfig;
  private isConnected = false;

  constructor(config: CacheConfig = {}) {
    this.config = {
      defaultTtl: 300, // 5 minutes default
      prefix: "relio:",
      ...config,
    };
  }

  async connect(): Promise<void> {
    if (this.isConnected) return;

    try {
      this.client = createClient({
        url: this.config.url || 
             (this.config.host ? `redis://${this.config.host}:${this.config.port || 6379}` : undefined),
        password: this.config.password,
      });

      this.client.on("error", (err) => {
        console.error("Redis Client Error:", err);
      });

      this.client.on("connect", () => {
        console.log("Redis Client Connected");
      });

      await this.client.connect();
      this.isConnected = true;
    } catch (error) {
      console.error("Failed to connect to Redis:", error);
      // Continue without cache if Redis is not available
      this.client = null;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      await this.client.disconnect();
      this.isConnected = false;
    }
  }

  private getKey(key: string, prefix?: string): string {
    const actualPrefix = prefix || this.config.prefix || "relio:";
    return `${actualPrefix}${key}`;
  }

  async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    if (!this.client || !this.isConnected) {
      return null;
    }

    try {
      const fullKey = this.getKey(key, options.prefix);
      const value = await this.client.get(fullKey);
      
      if (value === null) {
        return null;
      }

      return JSON.parse(value) as T;
    } catch (error) {
      console.error("Cache get error:", error);
      return null;
    }
  }

  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false;
    }

    try {
      const fullKey = this.getKey(key, options.prefix);
      const serializedValue = JSON.stringify(value);
      const ttl = options.ttl || this.config.defaultTtl || 300;

      await this.client.setEx(fullKey, ttl, serializedValue);
      return true;
    } catch (error) {
      console.error("Cache set error:", error);
      return false;
    }
  }

  async del(key: string, options: CacheOptions = {}): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false;
    }

    try {
      const fullKey = this.getKey(key, options.prefix);
      await this.client.del(fullKey);
      return true;
    } catch (error) {
      console.error("Cache delete error:", error);
      return false;
    }
  }

  async delPattern(pattern: string, options: CacheOptions = {}): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false;
    }

    try {
      const fullPattern = this.getKey(pattern, options.prefix);
      const keys = await this.client.keys(fullPattern);
      
      if (keys.length > 0) {
        await this.client.del(keys);
      }
      
      return true;
    } catch (error) {
      console.error("Cache delete pattern error:", error);
      return false;
    }
  }

  async exists(key: string, options: CacheOptions = {}): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false;
    }

    try {
      const fullKey = this.getKey(key, options.prefix);
      const exists = await this.client.exists(fullKey);
      return exists === 1;
    } catch (error) {
      console.error("Cache exists error:", error);
      return false;
    }
  }

  async ttl(key: string, options: CacheOptions = {}): Promise<number> {
    if (!this.client || !this.isConnected) {
      return -1;
    }

    try {
      const fullKey = this.getKey(key, options.prefix);
      return await this.client.ttl(fullKey);
    } catch (error) {
      console.error("Cache TTL error:", error);
      return -1;
    }
  }

  // Helper method for cache-aside pattern
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    // If not in cache, fetch the data
    const data = await fetcher();
    
    // Store in cache for next time
    await this.set(key, data, options);
    
    return data;
  }

  // Utility method to clear all cache with prefix
  async flush(prefix?: string): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      return false;
    }

    try {
      const pattern = prefix ? `${prefix}*` : `${this.config.prefix}*`;
      await this.delPattern(pattern);
      return true;
    } catch (error) {
      console.error("Cache flush error:", error);
      return false;
    }
  }

  isHealthy(): boolean {
    return this.isConnected && this.client !== null;
  }
}

export { CacheService, type CacheConfig, type CacheOptions }; 
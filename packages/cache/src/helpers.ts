import { CacheManager } from "./cache-manager.js";

interface CacheHelperConfig {
  cacheManager: CacheManager;
  enabled?: boolean;
}

let config: CacheHelperConfig | null = null;

export function configureCacheHelpers(helperConfig: CacheHelperConfig) {
  config = helperConfig;
}

// Helper for caching user data
export async function cacheUser<T>(userId: string, fetcher: () => Promise<T>): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("user", userId, fetcher);
}

// Helper for caching organization data
export async function cacheOrganization<T>(organizationId: string, fetcher: () => Promise<T>): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("organization", organizationId, fetcher);
}

// Helper for caching purchases
export async function cachePurchases<T>(
  organizationId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("purchases", organizationId, fetcher, params);
}

// Helper for caching notifications
export async function cacheNotifications<T>(
  userId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("notifications", userId, fetcher, params);
}

// Helper for caching tasks
export async function cacheTasks<T>(
  organizationId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("tasks", organizationId, fetcher, params);
}

// Helper for caching object views
export async function cacheObjectViews<T>(
  organizationId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("object-views", organizationId, fetcher, params);
}

// Helper for caching pins
export async function cachePins<T>(
  organizationId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("pins", organizationId, fetcher, params);
}

// Helper for caching favorites
export async function cacheFavorites<T>(
  organizationId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("favorites", organizationId, fetcher, params);
}

// Helper for caching objects (contacts, companies, properties)
export async function cacheObjects<T>(
  organizationId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("objects", organizationId, fetcher, params);
}

// Helper for caching custom field definitions
export async function cacheCustomFields<T>(
  organizationId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("custom-fields", organizationId, fetcher, params);
}

// Helper for caching connected accounts
export async function cacheConnectedAccounts<T>(
  userId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("connected-accounts", userId, fetcher, params);
}

// Helper for caching watermark data
export async function cacheWatermark<T>(
  organizationId: string, 
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet("watermark", organizationId, fetcher, params);
}

// Invalidation helpers
export async function invalidateUserCache(userId: string): Promise<void> {
  if (config?.cacheManager) {
    await config.cacheManager.invalidateUser(userId);
  }
}

export async function invalidateOrganizationCache(organizationId: string): Promise<void> {
  if (config?.cacheManager) {
    await config.cacheManager.invalidateOrganization(organizationId);
  }
}

// Generic cache wrapper
export async function withCache<T>(
  type: string,
  identifier: string,
  fetcher: () => Promise<T>,
  params?: Record<string, any>
): Promise<T> {
  if (!config?.enabled || !config.cacheManager.isHealthy()) {
    return fetcher();
  }

  return config.cacheManager.getOrSet(type, identifier, fetcher, params);
} 
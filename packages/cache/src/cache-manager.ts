import { CacheService, type CacheOptions } from "./cache.js";

interface CacheKeyConfig {
  prefix: string;
  ttl?: number;
  dependencies?: string[];
}

class CacheManager {
  private cache: CacheService;
  private keyConfigs: Map<string, CacheKeyConfig> = new Map();

  constructor(cache: CacheService) {
    this.cache = cache;
    this.setupKeyConfigs();
  }

  private setupKeyConfigs() {
    // Define cache key patterns and their configurations
    this.keyConfigs.set("user", {
      prefix: "user:",
      ttl: 600, // 10 minutes
    });

    this.keyConfigs.set("organization", {
      prefix: "org:",
      ttl: 300, // 5 minutes
    });

    this.keyConfigs.set("purchases", {
      prefix: "purchases:",
      ttl: 180, // 3 minutes
      dependencies: ["user", "organization"],
    });

    this.keyConfigs.set("notifications", {
      prefix: "notifications:",
      ttl: 60, // 1 minute
      dependencies: ["user"],
    });

    this.keyConfigs.set("tasks", {
      prefix: "tasks:",
      ttl: 120, // 2 minutes
      dependencies: ["organization"],
    });

    this.keyConfigs.set("object-views", {
      prefix: "views:",
      ttl: 300, // 5 minutes
      dependencies: ["organization"],
    });

    this.keyConfigs.set("pins", {
      prefix: "pins:",
      ttl: 300, // 5 minutes
      dependencies: ["organization"],
    });

    this.keyConfigs.set("favorites", {
      prefix: "favorites:",
      ttl: 300, // 5 minutes
      dependencies: ["organization"],
    });

    this.keyConfigs.set("objects", {
      prefix: "objects:",
      ttl: 240, // 4 minutes
      dependencies: ["organization"],
    });

    this.keyConfigs.set("custom-fields", {
      prefix: "custom-fields:",
      ttl: 600, // 10 minutes
      dependencies: ["organization"],
    });

    this.keyConfigs.set("connected-accounts", {
      prefix: "connected-accounts:",
      ttl: 300, // 5 minutes
      dependencies: ["user"],
    });

    this.keyConfigs.set("watermark", {
      prefix: "watermark:",
      ttl: 300, // 5 minutes
      dependencies: ["organization"],
    });
  }

  generateKey(type: string, identifier: string, params?: Record<string, any>): string {
    const config = this.keyConfigs.get(type);
    if (!config) {
      throw new Error(`Unknown cache type: ${type}`);
    }

    let key = `${config.prefix}${identifier}`;
    
    if (params) {
      // Sort params for consistent key generation
      const sortedParams = Object.keys(params)
        .sort()
        .map(k => `${k}:${params[k]}`)
        .join(":");
      
      if (sortedParams) {
        key += `:${sortedParams}`;
      }
    }

    return key;
  }

  async get<T>(type: string, identifier: string, params?: Record<string, any>): Promise<T | null> {
    const key = this.generateKey(type, identifier, params);
    return this.cache.get<T>(key);
  }

  async set<T>(
    type: string, 
    identifier: string, 
    value: T, 
    params?: Record<string, any>
  ): Promise<boolean> {
    const config = this.keyConfigs.get(type);
    if (!config) {
      throw new Error(`Unknown cache type: ${type}`);
    }

    const key = this.generateKey(type, identifier, params);
    const options: CacheOptions = { ttl: config.ttl };
    
    return this.cache.set(key, value, options);
  }

  async invalidate(type: string, identifier?: string, params?: Record<string, any>): Promise<boolean> {
    const config = this.keyConfigs.get(type);
    if (!config) {
      throw new Error(`Unknown cache type: ${type}`);
    }

    if (identifier) {
      // Invalidate specific key
      const key = this.generateKey(type, identifier, params);
      return this.cache.del(key);
    } else {
      // Invalidate all keys with this prefix
      const pattern = `${config.prefix}*`;
      return this.cache.delPattern(pattern);
    }
  }

  async invalidateByDependency(dependencyType: string, identifier: string): Promise<boolean> {
    let success = true;

    // Find all cache types that depend on this type
    for (const [type, config] of Array.from(this.keyConfigs.entries())) {
      if (config.dependencies?.includes(dependencyType)) {
        const result = await this.invalidate(type);
        if (!result) success = false;
      }
    }

    return success;
  }

  // Specialized methods for common patterns
  async invalidateUser(userId: string): Promise<boolean> {
    // Invalidate user-specific cache
    await this.invalidate("user", userId);
    
    // Invalidate caches that depend on user
    return this.invalidateByDependency("user", userId);
  }

  async invalidateOrganization(organizationId: string): Promise<boolean> {
    // Invalidate organization-specific cache
    await this.invalidate("organization", organizationId);
    
    // Invalidate caches that depend on organization
    return this.invalidateByDependency("organization", organizationId);
  }

  async getOrSet<T>(
    type: string,
    identifier: string,
    fetcher: () => Promise<T>,
    params?: Record<string, any>
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(type, identifier, params);
    if (cached !== null) {
      return cached;
    }

    // If not in cache, fetch the data
    const data = await fetcher();
    
    // Store in cache for next time
    await this.set(type, identifier, data, params);
    
    return data;
  }

  // Health check
  isHealthy(): boolean {
    return this.cache.isHealthy();
  }

  // Cache stats (for monitoring)
  async getStats(): Promise<Record<string, any>> {
    const stats: Record<string, any> = {
      healthy: this.isHealthy(),
      keyConfigs: Array.from(this.keyConfigs.keys()),
    };

    // Count keys by type
    for (const [type, config] of Array.from(this.keyConfigs.entries())) {
      try {
        const pattern = `${config.prefix}*`;
        // Note: This is expensive in production, consider removing or rate limiting
        // const keys = await this.cache.client?.keys(pattern) || [];
        // stats[`${type}_count`] = keys.length;
      } catch (error) {
        console.error(`Error getting stats for ${type}:`, error);
      }
    }

    return stats;
  }
}

export { CacheManager, type CacheKeyConfig }; 
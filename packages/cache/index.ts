export { CacheService, type CacheConfig, type CacheOptions } from "./src/cache";
export { CacheManager, type <PERSON>acheKeyConfig } from "./src/cache-manager";
export * from "./src/helpers";

// Create and export singleton instances for easy use
import { CacheService } from "./src/cache";
import { CacheManager } from "./src/cache-manager";

let cacheService: CacheService | null = null;
let cacheManager: CacheManager | null = null;

export function createCacheService(config?: any): CacheService {
  if (!cacheService) {
    cacheService = new CacheService(config);
  }
  return cacheService;
}

export function createCacheManager(cache?: CacheService): CacheManager {
  if (!cacheManager) {
    const cacheInstance = cache || createCacheService();
    cacheManager = new CacheManager(cacheInstance);
  }
  return cacheManager;
}

export function getCacheService(): CacheService | null {
  return cacheService;
}

export function getCacheManager(): CacheManager | null {
  return cacheManager;
}

// Initialize cache from environment variables
export async function initializeCache(): Promise<{ service: CacheService; manager: CacheManager }> {
  const config = {
    url: process.env.REDIS_URL,
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT) : undefined,
    password: process.env.REDIS_PASSWORD,
    defaultTtl: process.env.CACHE_DEFAULT_TTL ? parseInt(process.env.CACHE_DEFAULT_TTL) : undefined,
  };

  const service = createCacheService(config);
  const manager = createCacheManager(service);

  try {
    await service.connect();
    console.log("Cache service initialized successfully");
  } catch (error) {
    console.warn("Cache service failed to initialize, continuing without cache:", error);
  }

  return { service, manager };
} 
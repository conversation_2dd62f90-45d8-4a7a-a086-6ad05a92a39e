import { db } from "./client";

/**
 * Creates geospatial indexes for efficient property location queries
 * This enables fast geographic filtering for millions of properties
 */
export async function createGeoIndexes() {
	try {
		console.log("Creating geospatial indexes for property locations...");

		// Create 2d index for coordinates stored in PropertyLocation.location JSON field
		// This enables efficient geographic queries within bounding boxes
		await db.$runCommandRaw({
			createIndexes: "PropertyLocation",
			indexes: [
				{
					key: {
						"location.coordinates": "2d"
					},
					name: "geo_2d_index",
					background: true,
					sparse: true // Only index documents with coordinates
				},
				{
					// Compound index for organization + geospatial queries
					key: {
						propertyId: 1,
						"location.coordinates": "2d"
					},
					name: "property_geo_compound_index",
					background: true,
					sparse: true
				}
			]
		});

		// Create compound index for efficient property filtering by organization and location
		await db.$runCommandRaw({
			createIndexes: "Property",
			indexes: [
				{
					key: {
						organizationId: 1,
						isDeleted: 1,
						id: 1 // For cursor-based pagination
					},
					name: "org_deleted_cursor_index",
					background: true
				},
				{
					key: {
						organizationId: 1,
						isDeleted: 1,
						updatedAt: -1
					},
					name: "org_deleted_updated_index",
					background: true
				}
			]
		});

		console.log("✅ Geospatial indexes created successfully");
		return { success: true };

	} catch (error) {
		console.error("Failed to create geospatial indexes:", error);
		throw error;
	}
}

/**
 * Checks if geospatial indexes exist and creates them if missing
 */
export async function ensureGeoIndexes() {
	try {
		// Check if indexes exist
		const propertyLocationIndexes = await db.$runCommandRaw({
			listIndexes: "PropertyLocation"
		});

		const hasGeoIndex = (propertyLocationIndexes as any).cursor?.firstBatch?.some(
			(index: any) => index.name === "geo_2d_index"
		);

		if (!hasGeoIndex) {
			console.log("Geospatial indexes missing, creating them...");
			await createGeoIndexes();
		} else {
			console.log("✅ Geospatial indexes already exist");
		}

		return { success: true, created: !hasGeoIndex };

	} catch (error) {
		console.warn("Could not verify/create geospatial indexes:", error);
		// Don't throw - app should still work without indexes, just slower
		return { success: false, error: String(error) };
	}
}

/**
 * Advanced geospatial query builder for efficient property filtering
 */
export interface GeoBounds {
	north: number;
	south: number;
	east: number;
	west: number;
}

/**
 * Builds optimized geospatial query for MongoDB
 * Uses native MongoDB geospatial operators when available
 */
export function buildGeoQuery(bounds: GeoBounds, organizationId: string) {
	return {
		organizationId,
		isDeleted: false,
		location: {
			location: {
				// MongoDB $geoWithin query for efficient spatial filtering
				path: ['coordinates'],
				// Note: This would work with proper GeoJSON, but requires schema changes
				// For now, we'll filter in JavaScript for compatibility
			}
		}
	};
}

/**
 * Optimized coordinate validation with bounds checking
 */
export function validateGeoCoordinates(coords: any, bounds?: GeoBounds): coords is [number, number] {
	if (!Array.isArray(coords) || coords.length !== 2) {
		return false;
	}

	const [lng, lat] = coords;
	
	// Basic coordinate validation
	if (typeof lng !== 'number' || typeof lat !== 'number') {
		return false;
	}

	// World bounds validation
	if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
		return false;
	}

	// Optional bounds validation
	if (bounds) {
		return lng >= bounds.west && lng <= bounds.east && 
			   lat >= bounds.south && lat <= bounds.north;
	}

	return true;
}

/**
 * Calculates optimal grid size for clustering based on zoom level
 */
export function calculateClusterGridSize(zoom: number): number {
	// Adaptive grid sizing for different zoom levels
	if (zoom < 5) return 10.0;     // Very large clusters for world view
	if (zoom < 8) return 1.0;      // Large clusters for country view
	if (zoom < 10) return 0.1;     // Medium clusters for state view
	if (zoom < 12) return 0.01;    // Small clusters for city view
	if (zoom < 14) return 0.005;   // Tiny clusters for neighborhood view
	return 0.001;                  // Individual properties for street view
}

/**
 * Performance monitoring for geo queries
 */
export class GeoQueryPerformanceMonitor {
	private static metrics = new Map<string, { count: number; totalTime: number; avgTime: number }>();

	static startTimer(queryType: string) {
		return {
			queryType,
			startTime: Date.now(),
			end: () => this.endTimer(queryType, Date.now())
		};
	}

	private static endTimer(queryType: string, startTime: number) {
		const duration = Date.now() - startTime;
		const existing = this.metrics.get(queryType) || { count: 0, totalTime: 0, avgTime: 0 };
		
		existing.count++;
		existing.totalTime += duration;
		existing.avgTime = existing.totalTime / existing.count;
		
		this.metrics.set(queryType, existing);

		if (duration > 1000) { // Log slow queries
			console.warn(`Slow geo query detected: ${queryType} took ${duration}ms`);
		}
	}

	static getMetrics() {
		return Object.fromEntries(this.metrics.entries());
	}

	static resetMetrics() {
		this.metrics.clear();
	}
} 
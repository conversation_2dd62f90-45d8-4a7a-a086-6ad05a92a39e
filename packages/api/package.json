{"dependencies": {"@ai-sdk/anthropic": "^1.1.10", "@ai-sdk/openai": "^1.3.22", "@hono/zod-openapi": "^0.19.8", "@inngest/workflow-kit": "^0.1.3", "@repo/ai": "workspace:*", "@repo/auth": "workspace:*", "@repo/cache": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@scalar/hono-api-reference": "^0.5.175", "@sindresorhus/slugify": "^2.2.1", "ai": "4.3.16", "hono": "^4.8.2", "hono-openapi": "^0.4.5", "inngest": "^3.39.1", "nanoid": "^5.1.2", "openai": "^4.85.4", "openapi-merge": "^1.3.3", "redis": "^4.6.12", "use-intl": "^3.26.5", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/react": "19.0.0", "encoding": "^0.1.13", "prisma": "^6.10.1", "typescript": "5.8.2"}, "main": "./index.ts", "name": "@repo/api", "scripts": {"type-check": "tsc --noEmit"}, "version": "0.0.0"}
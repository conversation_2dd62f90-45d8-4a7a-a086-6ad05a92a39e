import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { cacheHelpers } from "../../lib/redis-cache";
import { ObjectId } from "mongodb";

export const pinsRouter = new Hono<{ Variables: { user: Session["user"] } }>();

// Schema for creating a pin
const createPinSchema = z.object({
	objectId: z.string(),
	objectType: z.string(),
	name: z.string(),
	image: z.string().optional(),
	organizationId: z.string(),
});

// Schema for deleting a pin
const deletePinSchema = z.object({
	objectId: z.string(),
	objectType: z.string(),
	organizationId: z.string(),
});

// Create a pin
pinsRouter.post("/pins", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();
		const { objectId, objectType, name, image, organizationId } =
			createPinSchema.parse(body);
		// Check if pin already exists
		const existingPin = await db.pin.findUnique({
			where: {
				userId_organizationId_objectId_objectType: {
					userId: user.id,
					organizationId,
					objectId,
					objectType,
				},
			},
		});

		if (existingPin) {
			return c.json({ error: "Record is already pinned" }, 400);
		}

		// Get the highest position for ordering
		const lastPin = await db.pin.findFirst({
			where: {
				userId: user.id,
				organizationId,
			},
			orderBy: {
				position: "desc",
			},
		});

		const position = lastPin ? lastPin.position! + 1 : 1;

		const pin = await db.pin.create({
			data: {
				id: new ObjectId().toString(),
				userId: user.id,
				organizationId,
				objectId,
				objectType,
				name,
				image,
				position,
			},
		});

		// Invalidate pins cache
		await cacheHelpers.pins.invalidate(user.id, organizationId);
		
		logger.info(`Pin created: ${pin.id} for ${objectType}:${objectId}`);
		return c.json(pin);
	} catch (error) {
		logger.error("Failed to create pin:", error);
		return c.json({ error: "Failed to create pin" }, 500);
	}
});

// Delete a pin
pinsRouter.delete("/pins", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();
		const { objectId, objectType, organizationId } =
			deletePinSchema.parse(body);

		const pin = await db.pin.findUnique({
			where: {
				userId_organizationId_objectId_objectType: {
					userId: user.id,
					organizationId,
					objectId,
					objectType,
				},
			},
		});

		if (!pin) {
			return c.json({ error: "Pin not found" }, 404);
		}

		await db.pin.delete({
			where: {
				id: pin.id,
			},
		});

		// Invalidate pins cache
		await cacheHelpers.pins.invalidate(user.id, organizationId);
		
		logger.info(`Pin deleted: ${pin.id} for ${objectType}:${objectId}`);
		return c.json({ success: true });
	} catch (error) {
		logger.error("Failed to delete pin:", error);
		return c.json({ error: "Failed to delete pin" }, 500);
	}
});

// Get all pins for a user in an organization
pinsRouter.get("/pins", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const organizationId = c.req.query("organizationId");
		const objectType = c.req.query("objectType");

		if (!organizationId) {
			return c.json({ error: "Organization ID is required" }, 400);
		}

		const pins = await cacheHelpers.pins.get(user.id, organizationId, objectType || undefined);

		return c.json(pins);
	} catch (error) {
		logger.error("Failed to fetch pins:", error);
		return c.json({ error: "Failed to fetch pins" }, 500);
	}
});

// Delete all pins for a user in an organization
pinsRouter.delete("/all", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();
		const organizationId = z.string().parse(body.organizationId);

		await db.pin.deleteMany({
			where: {
				userId: user.id,
				organizationId,
			},
		});

		logger.info(`All pins deleted for organization: ${organizationId}`);
		return c.json({ success: true });
	} catch (error) {
		logger.error("Failed to delete all pins:", error);
		return c.json({ error: "Failed to delete all pins" }, 500);
	}
});

// Update pin order
pinsRouter.post("/pins/order", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();
		const schema = z.object({
			organizationId: z.string(),
			pinIds: z.array(z.string()),
		});
		const { organizationId, pinIds } = schema.parse(body);

		// Fetch all pins for this user/org
		const pins = await db.pin.findMany({
			where: {
				userId: user.id,
				organizationId,
			},
		});

		// Map pin IDs to pins for quick lookup
		const pinMap = new Map(pins.map((pin) => [pin.id, pin]));

		// Only update pins that belong to the user/org
		const updates = pinIds.map((id, idx) => {
			const pin = pinMap.get(id);
			if (!pin) return null;
			return db.pin.update({
				where: { id },
				data: { position: idx + 1 },
			});
		});

		await Promise.all(updates.filter(Boolean));

		logger.info(`Pin order updated for organization: ${organizationId}`);
		return c.json({ success: true });
	} catch (error) {
		logger.error("Failed to update pin order:", error);
		return c.json({ error: "Failed to update pin order" }, 500);
	}
});

export type PinsRouter = typeof pinsRouter;

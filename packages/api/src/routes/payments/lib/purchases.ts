import { db } from "@repo/database/server";
import { cacheHelpers } from "../../../lib/redis-cache";

export const getPurchases = async (
	props: { organizationId: string } | { userId: string },
) => {
	try {
		const cachedResult = await cacheHelpers.purchases.get(props);
		if (cachedResult) {
			return cachedResult;
		}
	} catch (error) {
		console.warn("Failed to get purchases from cache:", error);
	}

	return await db.purchase.findMany({
		where: props,
	});
};

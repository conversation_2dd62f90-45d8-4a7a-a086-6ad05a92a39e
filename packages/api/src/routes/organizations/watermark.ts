import { getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database/server";
import { OrganizationSchema } from "@repo/database";
import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { cacheHelpers } from "../../lib/redis-cache";

export const watermarkRouter = new Hono()
	.put(
		"/",
		authMiddleware,
		validator(
			"json",
			z.object({
				organizationId: z.string(),
				enabled: z.boolean(),
			}),
		),
		describeRoute({
			tags: ["Organizations"],
			summary: "Update email watermark setting",
			description: "Update the email watermark preference for an organization. Free accounts cannot disable the watermark.",
			responses: {
				200: {
					description: "Updated organization",
					content: {
						"application/json": {
							schema: resolver(OrganizationSchema),
						},
					},
				},
				403: {
					description: "Forbidden - insufficient permissions or free account trying to disable watermark",
				},
			},
		}),
		async (c) => {
			const { organizationId, enabled } = c.req.valid("json");
			const user = c.get("user");

			// Check if user has permission to modify organization settings
			const membership = await getOrganizationMembership(user.id, organizationId);
			if (!membership || membership.role !== "owner") {
				throw new HTTPException(403, { message: "Insufficient permissions" });
			}

			// Check if organization exists and get purchases separately
			const organization = await db.organization.findUnique({
				where: { id: organizationId },
			});

			if (!organization) {
				throw new HTTPException(404, { message: "Organization not found" });
			}

			// Check if trying to disable watermark on free account
			const purchases = await cacheHelpers.purchases.get({ organizationId });

			const hasPaidPlan = purchases.length > 0;
			if (!enabled && !hasPaidPlan) {
				throw new HTTPException(403, { 
					message: "Cannot disable watermark on free plan. Upgrade to a paid plan to remove the watermark." 
				});
			}

			// Update the watermark setting using any cast temporarily
			const updatedOrganization = await db.organization.update({
				where: { id: organizationId },
				data: { emailWatermarkEnabled: enabled } as any,
			});

			return c.json(updatedOrganization);
		},
	)
	.get(
		"/:organizationId",
		authMiddleware,
		validator(
			"param",
			z.object({
				organizationId: z.string(),
			}),
		),
		describeRoute({
			tags: ["Organizations"],
			summary: "Get email watermark setting",
			description: "Get the current email watermark preference for an organization",
			responses: {
				200: {
					description: "Watermark settings",
					content: {
						"application/json": {
							schema: resolver(z.object({
								enabled: z.boolean(),
								canDisable: z.boolean(),
							})),
						},
					},
				},
			},
		}),
		async (c) => {
			const { organizationId } = c.req.valid("param");
			const user = c.get("user");

			// Check if user has access to organization
			const membership = await getOrganizationMembership(user.id, organizationId);
			if (!membership) {
				throw new HTTPException(403, { message: "Access denied" });
			}

			// Get organization and purchases separately
			const organization = await db.organization.findUnique({
				where: { id: organizationId },
			});

			if (!organization) {
				throw new HTTPException(404, { message: "Organization not found" });
			}

			const purchases = await cacheHelpers.purchases.get({ organizationId });

			const hasPaidPlan = purchases.length > 0;

			return c.json({
				enabled: (organization as any).emailWatermarkEnabled ?? true,
				canDisable: hasPaidPlan, // Only paid plans can disable watermark
			});
		},
	); 
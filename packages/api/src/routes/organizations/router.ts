import { db } from "@repo/database/server";
import slugify from "@sindresorhus/slugify";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { nanoid } from "nanoid";
import { z } from "zod";
import { watermarkRouter } from "./watermark";

export const organizationsRouter = new Hono()
	.basePath("/organizations")
	.route("/watermark", watermarkRouter)
	.get(
		"/generate-slug",
		validator(
			"query",
			z.object({
				name: z.string(),
			}),
		),
		describeRoute({
			summary: "Generate a slug for an organization",
			tags: ["Organizations"],
		}),
		async (c) => {
			const { name } = c.req.valid("query");

			if (!name || name.trim().length < 2) {
				return c.json(
					{
						error: "Organization name must be at least 2 characters long",
					},
					400,
				);
			}

			const baseSlug = slugify(name, {
				lowercase: true,
			});

			if (!baseSlug) {
				return c.json(
					{
						error: "Could not generate a valid slug from the organization name. Please use a different name.",
					},
					400,
				);
			}

			let slug = baseSlug;
			let hasAvailableSlug = false;
			const maxAttempts = 10; // Increase max attempts

			for (let i = 0; i < maxAttempts; i++) {
				const existing = await db.organization.findUnique({
					where: {
						slug,
					},
				});

				if (!existing) {
					hasAvailableSlug = true;
					break;
				}

				// Try different slug formats for better uniqueness
				if (i < 3) {
					// First try with short random strings
					slug = `${baseSlug}-${nanoid(3)}`;
				} else if (i < 6) {
					// Then try with longer random strings
					slug = `${baseSlug}-${nanoid(5)}`;
				} else {
					// Finally try with timestamp-based suffix
					slug = `${baseSlug}-${Date.now().toString(36)}`;
				}
			}

			if (!hasAvailableSlug) {
				return c.json(
					{
						error: "Could not generate a unique URL for this organization name. Please try a different name.",
					},
					400,
				);
			}

			return c.json({
				slug,
			});
		},
	)
	// Custom organization cookie endpoint (renamed to avoid conflict with better-auth)
	.post("/set-active-cookie", async (c) => {
		const { organizationId } = await c.req.json();
		if (!organizationId) {
			return c.json({ error: "organizationId is required" }, 400);
		}
		c.header(
			"Set-Cookie",
			`activeOrganizationId=${organizationId}; Path=/; HttpOnly; SameSite=Lax`,
		);
		return c.json({ success: true });
	});

import { createClient, type RedisClientType } from 'redis';
import { db } from "@repo/database/server";

class RedisCache {
  private client: RedisClientType | null = null;
  private isConnecting = false;
  private isEnabled = false; // Start disabled by default
  private connectionPromise: Promise<void> | null = null;

  constructor() {
    // Don't connect immediately, wait for first use
    this.isEnabled = !!process.env.REDIS_URL;
  }

  private async connect() {
    if (this.isConnecting || this.client?.isReady) {
      return this.connectionPromise;
    }
    
    if (!this.isEnabled) {
      return Promise.resolve();
    }
    
    this.isConnecting = true;
    
    this.connectionPromise = new Promise<void>(async (resolve, reject) => {
      try {
        const redisUrl = process.env.REDIS_URL;
        if (!redisUrl) {
          this.isEnabled = false;
          resolve();
          return;
        }
        
        this.client = createClient({
          url: redisUrl,
          socket: {
            connectTimeout: 5000,
            reconnectStrategy: (retries) => {
              if (retries > 3) {
                this.isEnabled = false;
                return new Error('Redis connection failed, disabling cache');
              }
              return Math.min(retries * 100, 3000);
            }
          },
        });

        this.client.on('error', (err: Error) => {
          console.warn('[REDIS] Connection error:', err.message);
          this.isEnabled = false;
        });

        this.client.on('connect', () => {
          console.log('[REDIS] Connected successfully');
          this.isEnabled = true;
        });

        await this.client.connect();
        resolve();
      } catch (error) {
        console.warn('[REDIS] Failed to connect:', error);
        this.isEnabled = false;
        this.client = null;
        resolve(); // Resolve anyway to continue without cache
      } finally {
        this.isConnecting = false;
      }
    });

    return this.connectionPromise;
  }

  async get<T>(key: string): Promise<T | null> {
    if (!this.isEnabled) return null;
    
    try {
      await this.connect();
      
      if (!this.client?.isReady) {
        return null;
      }
      
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn('[REDIS] Get error:', error);
      return null;
    }
  }

  async set(key: string, value: any, ttlSeconds = 300): Promise<void> {
    if (!this.isEnabled) return;
    
    try {
      await this.connect();
      
      if (!this.client?.isReady) {
        return;
      }
      
      await this.client.setEx(key, ttlSeconds, JSON.stringify(value));
    } catch (error) {
      console.warn('[REDIS] Set error:', error);
    }
  }

  async del(pattern: string): Promise<void> {
    if (!this.isEnabled) return;
    
    try {
      await this.connect();
      
      if (!this.client?.isReady) {
        return;
      }
      
      if (pattern.includes('*')) {
        // Pattern-based deletion
        const keys = await this.client.keys(pattern);
        if (keys.length > 0) {
          await this.client.del(keys);
        }
      } else {
        // Single key deletion
        await this.client.del(pattern);
      }
    } catch (error) {
      console.warn('[REDIS] Delete error:', error);
    }
  }

  async withCache<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttlSeconds = 300
  ): Promise<T> {
    try {
      // Try to get from cache first
      const cached = await this.get<T>(key);
      if (cached !== null) {
        return cached;
      }
    } catch (error) {
      console.warn('[REDIS] Cache get error:', error);
    }

    // Fetch fresh data
    const data = await fetcher();
    
    // Try to cache for next time (don't await)
    this.set(key, data, ttlSeconds).catch(err => 
      console.warn('[REDIS] Background cache set error:', err)
    );
    
    return data;
  }
}

// Export singleton instance
export const redisCache = new RedisCache();

// Helper functions for specific data types
export const cacheHelpers = {
  notifications: {
    get: (userId: string) => redisCache.withCache(
      `notifications:user:${userId}`,
      async () => {
        return await db.notification.findMany({
          where: { userId },
          orderBy: { createdAt: "desc" },
        });
      },
      60 // 1 minute TTL for notifications
    ),
    invalidate: (userId: string) => redisCache.del(`notifications:user:${userId}`)
  },
  
  tasks: {
    get: (organizationId: string, filters?: any) => {
      const filterKey = filters ? btoa(JSON.stringify(filters)) : 'all';
      return redisCache.withCache(
        `tasks:org:${organizationId}:${filterKey}`,
        async () => {
          return await db.task.findMany({
            where: filters || { organizationId },
            orderBy: { createdAt: "desc" },
            include: {
              assignee: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          });
        },
        300 // 5 minutes TTL for tasks
      );
    },
    invalidate: (organizationId: string) => redisCache.del(`tasks:org:${organizationId}:*`)
  },

  purchases: {
    get: (props: { organizationId: string } | { userId: string }) => {
      const key = 'organizationId' in props 
        ? `purchases:org:${props.organizationId}`
        : `purchases:user:${props.userId}`;
      
      return redisCache.withCache(
        key,
        async () => {
          return await db.purchase.findMany({
            where: props,
          });
        },
        600 // 10 minutes TTL for purchases
      );
    },
    invalidate: (props: { organizationId: string } | { userId: string }) => {
      const pattern = 'organizationId' in props 
        ? `purchases:org:${props.organizationId}`
        : `purchases:user:${props.userId}`;
      return redisCache.del(pattern);
    }
  },

  objects: {
    get: (organizationId: string, objectType: string, filters?: any) => {
      const filterKey = filters ? btoa(JSON.stringify(filters)) : 'all';
      return redisCache.withCache(
        `objects:${objectType}:org:${organizationId}:${filterKey}`,
        async () => {
          // This would need to be implemented based on the actual objects query
          // For now, returning empty array as placeholder
          return [];
        },
        300 // 5 minutes TTL
      );
    },
    invalidate: (organizationId: string, objectType?: string) => {
      const pattern = objectType 
        ? `objects:${objectType}:org:${organizationId}:*`
        : `objects:*:org:${organizationId}:*`;
      return redisCache.del(pattern);
    }
  },

  pins: {
    get: (userId: string, organizationId: string, objectType?: string) => {
      const key = objectType 
        ? `pins:user:${userId}:org:${organizationId}:type:${objectType}`
        : `pins:user:${userId}:org:${organizationId}:all`;
      
      return redisCache.withCache(
        key,
        async () => {
          return await db.pin.findMany({
            where: {
              userId,
              organizationId,
              ...(objectType && { objectType }),
            },
            orderBy: {
              position: "asc",
            },
          });
        },
        300 // 5 minutes TTL for pins
      );
    },
    invalidate: (userId: string, organizationId: string) => redisCache.del(`pins:user:${userId}:org:${organizationId}:*`)
  },

  watermark: {
    get: (organizationId: string) => redisCache.withCache(
      `watermark:org:${organizationId}`,
      async () => {
        const organization = await db.organization.findUnique({
          where: { id: organizationId },
          select: { emailWatermarkEnabled: true }
        });
        return organization?.emailWatermarkEnabled;
      },
      600 // 10 minutes TTL for watermark settings
    ),
    invalidate: (organizationId: string) => redisCache.del(`watermark:org:${organizationId}`)
  }
}; 